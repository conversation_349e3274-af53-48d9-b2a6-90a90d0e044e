
.statusNotification{
    padding: 10px 15px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center; 
    color: white;
    font-family: "Inter";
    position: fixed;
    top: 0.5rem;
    left: 50%;
    transform: translate(-50%); 
    max-width: 450px;
    background-color:#252525;
    border: 1px solid #787878;
    width: auto;
    box-shadow: 0 0 10px 0 rgba(0, 0, 0, 0.5);
    &-box{
        display: flex;
        width: 100%;
        // flex-direction: row;
        align-items: center;
        gap: 10px;
        position: relative;
        .statusNotification-close {
            // position: absolute;
            // top: 50%;
            // left: 97%;
            // transform: translate(-50%, -50%);
            background: none;
            border: none;
            cursor: pointer;
            padding: 5px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #787878;
      
            &:hover {
              opacity: 0.8;
            }
          }
        &-circle{
            // &-outer{
            //     background-color: white;
            //     border-radius: 50%;
            //     width: 3rem;
            //     height: 3rem;
            //     display: flex;
            //     justify-content: center;
            //     align-items: center;
            // }
            &-inner{
                width: 1.5rem;
                height: 1.5rem;
                display: flex;
                justify-content: center;
                align-items: center;
                font-size: 20px;
                border-radius: 50%;
            }
        }
    }
    &-text{
        max-width: 400px;
        width: auto;
        white-space: nowrap;
        span{
            font-size: 16px;
        }
    }
    &-success{
        background-color: #00CC99;
        color: white;
        // border-radius: 50%;
        // color: white;
        // // &-bg{
        // //     background-color: #DDF1EC;
        // // }
    }
    &-error{
        background-color: #eb5757;
        color: white;
        // border-radius: 50%;
        // &-bg{
        //     background-color: #F4E6E6;
        // }
    }
    &-warning{
        background-color:#F2C94C;
        color: #000;
        // border-radius: 50%;
        color: black;
        // &-bg{
        //     background-color: #F4EFDF;
        // }
    }
    &-info{
        background-color: #5458F7;
        color: white;
    }
    &-check{
        color: #120F0F;
    }
    &-content{
        &-bg{
            width: 250px;
            padding-top: 1rem;
            .statusNotification-text{
                width: 100%;
            }
            .pwj{
                &-toast{
                    display: flex;
                    flex-direction: column;
                    gap: 0.5rem;
                    p{
                        margin: 0;
                    }
                }
                &-name-card{
                    display: flex;
                    align-items: center;
                    gap: 0.5rem;
                    span{
                        font-size: 14px;
                    }
                }
                &-actions{
                    display: flex;
                    justify-content: flex-end;
                    button{
                        &:nth-child(2){
                            color: white;
                        }
                    }
                }
            }
            .statusNotification-close{
                position: absolute;
                color: white;
                top: 0;
                right: 0;
            }
        }
    }
}